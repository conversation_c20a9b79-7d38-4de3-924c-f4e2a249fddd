/**
 * 个人工具函数集合
 * 包含通用的工具函数
 */

/**
 * HTTP响应头接口
 */
export interface ResponseHeaders {
  [key: string]: string | string[] | undefined;
  "content-disposition"?: string;
  "content-type"?: string;
  "content-length"?: string;
}

/**
 * 文件下载结果接口
 */
export interface DownloadResult {
  data: Blob;
  headers: ResponseHeaders;
}

/**
 * 消息服务接口
 */
export interface MessageService {
  loading(content: string, duration?: number): void;
  success(content: string, duration?: number): void;
  error(content: string, duration?: number): void;
  destroy(): void;
}

/**
 * 下载配置选项接口
 */
export interface DownloadOptions {
  /** 文件名，如果不提供则尝试从响应头获取 */
  fileName?: string;
  /** 是否显示消息提示，默认为 false */
  showMessage?: boolean;
  /** 消息服务实例，当 showMessage 为 true 时必须提供 */
  messageService?: MessageService;
}

/**
 * 根据MIME类型获取文件扩展名
 * @param mimeType - MIME类型
 * @returns 文件扩展名（包含点号）
 */
function getFileExtensionFromMimeType(mimeType: string): string {
  const mimeToExtension: Record<string, string> = {
    "application/pdf": ".pdf",
    "application/vnd.ms-excel": ".xls",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
      ".xlsx",
    "application/msword": ".doc",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
      ".docx",
    "application/vnd.ms-powerpoint": ".ppt",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation":
      ".pptx",
    "text/plain": ".txt",
    "text/csv": ".csv",
    "application/json": ".json",
    "application/xml": ".xml",
    "text/xml": ".xml",
    "image/jpeg": ".jpg",
    "image/png": ".png",
    "image/gif": ".gif",
    "image/bmp": ".bmp",
    "image/webp": ".webp",
    "application/zip": ".zip",
    "application/x-rar-compressed": ".rar",
    "application/x-7z-compressed": ".7z",
    "video/mp4": ".mp4",
    "video/avi": ".avi",
    "audio/mpeg": ".mp3",
    "audio/wav": ".wav",
  };

  return mimeToExtension[mimeType.toLowerCase()] || "";
}

/**
 * 简化的文件下载函数 - 推荐使用
 * @param fileData - 文件数据（Blob 或包含 data 和 headers 的对象）
 * @param options - 下载配置选项
 * @returns boolean - 下载是否成功
 */
export function downloadFileWithMessage(
  fileData: Blob | DownloadResult | null,
  options: DownloadOptions = {}
): boolean {
  if (!fileData) {
    console.error("文件数据为空");
    return false;
  }

  try {
    let blob: Blob;
    let fileName = options.fileName || "download";

    // 处理不同类型的文件数据
    if (fileData instanceof Blob) {
      blob = fileData;
    } else if (fileData && typeof fileData === "object" && "data" in fileData) {
      blob = fileData.data;

      // 尝试从响应头获取文件名
      if (!options.fileName && fileData.headers?.["content-disposition"]) {
        const disposition = fileData.headers["content-disposition"];
        const matches = disposition.match(
          /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
        );
        if (matches && matches[1]) {
          fileName = matches[1].replace(/['"]/g, "");
        }
      }

      // 如果仍然没有文件名或文件名没有扩展名，尝试从content-type推断
      if (
        !options.fileName &&
        (!fileName || fileName === "download" || !fileName.includes("."))
      ) {
        const contentType = fileData.headers?.["content-type"];
        if (contentType) {
          const extension = getFileExtensionFromMimeType(contentType);
          if (extension) {
            fileName =
              fileName === "download"
                ? `download${extension}`
                : `${fileName}${extension}`;
          } else {
            console.warn(
              `无法从content-type "${contentType}" 推断文件扩展名，文件将以 "${fileName}" 下载`
            );
          }
        } else {
          console.warn("无法确定文件类型，建议在调用时提供fileName参数");
        }
      }
    } else {
      console.error("不支持的文件数据格式");
      return false;
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = fileName;

    // 添加到DOM并触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理URL对象
    window.URL.revokeObjectURL(url);

    // 显示成功消息
    if (options.showMessage && options.messageService) {
      options.messageService.success(`下载成功: ${fileName}`);
    }

    return true;
  } catch (error) {
    console.error("文件下载失败:", error);

    // 显示错误消息
    if (options.showMessage && options.messageService) {
      options.messageService.error("下载失败，请重试");
    }

    return false;
  }
}
